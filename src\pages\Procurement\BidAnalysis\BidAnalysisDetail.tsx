import { useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { Screen } from "@/app-components/layout/screen";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  useGetBidAnalysisQuery,
  useGetBidAnalysisLinesQuery,
  useDeleteBidAnalysisMutation,
  useGeneratePurchaseOrderFromBidAnalysisMutation,
} from "@/redux/slices/procurement";
import { useBidAnalysisUpdater } from "@/hooks/useBidAnalysisUpdater";
import { BidAnalysisService } from "@/services/bidAnalysisService";
import {
  ArrowLeft,
  Send,
  CheckCircle,
  XCircle,
  FileText,
  Calendar,
  User,
  Building,
  Package,
  Loader2,
  Download,
  MapPin,
  Clock,
  DollarSign,
  TrendingUp,
  Crown,
  AlertTriangle
} from "lucide-react";
import { toast } from "@/components/custom/Toast/MyToast";
// Note: Removed old components that don't exist in new API

const BidAnalysisDetail = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  const { data: bidAnalysis, isLoading, error } = useGetBidAnalysisQuery(id!);
  const { data: bidAnalysisLines } = useGetBidAnalysisLinesQuery({ bid_analysis: id });

  const [deleteBidAnalysis, { isLoading: deleting }] = useDeleteBidAnalysisMutation();
  const [generatePurchaseOrder, { isLoading: generatingPO }] = useGeneratePurchaseOrderFromBidAnalysisMutation();
  const [purchaseOrdersCreated, setPurchaseOrdersCreated] = useState<string[]>([]);

  // Auto-update bid analysis when lines change
  useBidAnalysisUpdater(bidAnalysis, bidAnalysisLines?.data?.results);

  // Calculate supplier summary and validation
  const supplierSummary = bidAnalysisLines?.data?.results ? BidAnalysisService.calculateSummary(bidAnalysisLines.data.results) : null;
  const validation = bidAnalysisLines?.data?.results ? BidAnalysisService.validateSupplierSelection(bidAnalysisLines.data.results) : null;

  const handleDelete = async () => {
    if (window.confirm("Are you sure you want to delete this bid analysis?")) {
      try {
        await deleteBidAnalysis(Number(id)).unwrap();
        toast.success("Bid analysis deleted successfully");
        navigate("/procurement/bid-analysis");
      } catch (error: any) {
        toast.error(error?.data?.message || "Failed to delete bid analysis");
      }
    }
  };

  const handleCreatePurchaseOrder = async () => {
    if (window.confirm("Create Purchase Order from this bid analysis? This will generate PO(s) for the selected suppliers.")) {
      try {
        const payload = {
          code: bidAnalysis.code || `BA-${String(bidAnalysis.id).padStart(4, '0')}`,
          rfq: bidAnalysis.rfq_number || `RFQ-${String(bidAnalysis.rfq).padStart(4, '0')}`,
          created_by: bidAnalysis.created_by_name || bidAnalysis.created_by,
          split_award: bidAnalysis.split_award,
          recommendation_notes: bidAnalysis.recommendation_notes,
          selected_responses: bidAnalysis.selected_responses,
          finalized_at: bidAnalysis.finalized_at,
        };

        const result = await generatePurchaseOrder({
          id: Number(id),
          ...payload
        }).unwrap();

        // Track successful PO creation
        const createdPOs = Array.isArray(result) ? result.map((po: any) => po.po_number) : [result.po_number];
        setPurchaseOrdersCreated(createdPOs);

        toast.success(`Purchase Order(s) created successfully: ${createdPOs.join(', ')}`);

        // Navigate to purchase orders page to see the created POs
        navigate("/procurement/purchase-orders");
      } catch (error: any) {
        console.error("Error creating purchase order:", error);

        // If POs already exist, extract and store the PO numbers
        if (error?.data?.detail?.includes("already generated") && error?.data?.pos) {
          setPurchaseOrdersCreated(error.data.pos);
          toast.error(`Purchase Order(s) already exist: ${error.data.pos.join(', ')}`);
        } else {
          toast.error(error?.data?.message || "Failed to create purchase order");
        }
      }
    }
  };



  if (isLoading) {
    return (
      <Screen>
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </Screen>
    );
  }

  if (error || !bidAnalysis) {
    return (
      <Screen>
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Bid Analysis Not Found</h2>
          <p className="text-gray-600 mb-4">The bid analysis you're looking for doesn't exist or has been removed.</p>
          <Button onClick={() => navigate("/procurement/bid-analysis")}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Bid Analysis
          </Button>
        </div>
      </Screen>
    );
  }

  const linesCount = bidAnalysisLines?.data?.results?.length || 0;

  return (
    <Screen>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate("/procurement/bid-analysis")}
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-800">
                Bid Analysis BA-{String(bidAnalysis.id).padStart(4, '0')}
              </h1>
              <div className="flex items-center gap-2 mt-1">
                <Badge variant={bidAnalysis.split_award ? "default" : "secondary"}>
                  {bidAnalysis.split_award ? "Split Award" : "Single Award"}
                </Badge>
                <span className="text-gray-500">•</span>
                <span className="text-gray-600">
                  Finalized {new Date(bidAnalysis.finalized_at).toLocaleDateString()}
                </span>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center gap-2">
            {bidAnalysis.finalized_at && purchaseOrdersCreated.length === 0 && (
              <Button
                variant="default"
                onClick={handleCreatePurchaseOrder}
                disabled={generatingPO}
                className="bg-green-600 hover:bg-green-700"
              >
                {generatingPO ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Package className="mr-2 h-4 w-4" />
                )}
                Create Purchase Order
              </Button>
            )}

            {bidAnalysis.finalized_at && purchaseOrdersCreated.length > 0 && (
              <Button
                variant="outline"
                disabled
                className="bg-blue-50 border-blue-200 text-blue-700"
              >
                <CheckCircle className="mr-2 h-4 w-4" />
                PO Created: {purchaseOrdersCreated.join(', ')}
              </Button>
            )}
            <Button
              variant="destructive"
              onClick={handleDelete}
              disabled={deleting}
            >
              {deleting ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <XCircle className="mr-2 h-4 w-4" />
              )}
              Delete Analysis
            </Button>
          </div>
        </div>

        {/* Analysis Summary */}
        <Card>
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{linesCount}</div>
                <div className="text-sm text-gray-600">Analysis Lines</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {bidAnalysis.selected_responses?.length || 0}
                </div>
                <div className="text-sm text-gray-600">Selected Responses</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {bidAnalysis.split_award ? "Split" : "Single"}
                </div>
                <div className="text-sm text-gray-600">Award Type</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Analysis Details */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Analysis Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 gap-4">
                <div>
                  <Label className="text-sm font-medium text-gray-500">RFQ ID</Label>
                  <p className="font-medium">RFQ-{String(bidAnalysis.rfq).padStart(4, '0')}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Created By</Label>
                  <p className="font-medium">User ID: {bidAnalysis.created_by}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Finalized At</Label>
                  <p className="font-medium">
                    {new Date(bidAnalysis.finalized_at).toLocaleString()}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Recommendation Notes
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-700 whitespace-pre-wrap">
                {bidAnalysis.recommendation_notes}
              </p>
            </CardContent>
          </Card>
          {/* Bid Analysis Lines */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                Analysis Lines ({linesCount})
              </CardTitle>
            </CardHeader>
            <CardContent>
              {linesCount > 0 ? (
                <div className="space-y-4">
                  {bidAnalysisLines?.data?.results?.map((line: any, index: number) => (
                    <div key={line.id} className="p-4 border rounded-lg">
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <div>
                          <Label className="text-sm font-medium text-gray-500">Unit Price</Label>
                          <p className="font-medium">{line.unit_price}</p>
                        </div>
                        <div>
                          <Label className="text-sm font-medium text-gray-500">Quantity Awarded</Label>
                          <p className="font-medium">{line.quantity_awarded}</p>
                        </div>
                        <div>
                          <Label className="text-sm font-medium text-gray-500">Currency</Label>
                          <p className="font-medium">{line.currency}</p>
                        </div>
                        <div>
                          <Label className="text-sm font-medium text-gray-500">Delivery Time</Label>
                          <p className="font-medium">
                            {line.delivery_time_days ? `${line.delivery_time_days} days` : "Not specified"}
                          </p>
                        </div>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                        <div>
                          <Label className="text-sm font-medium text-gray-500">RFQ Item ID</Label>
                          <p className="font-medium">{line.rfq_item}</p>
                        </div>
                        <div>
                          <Label className="text-sm font-medium text-gray-500">Supplier ID</Label>
                          <p className="font-medium">{line.supplier}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <Package className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p>No analysis lines found</p>
                  <p className="text-sm">Analysis lines will appear here once they are created</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </Screen>
  );
};

export default BidAnalysisDetail;
