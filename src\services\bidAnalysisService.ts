import { BidAnalysis, BidAnalysisLine } from "@/types/procurement";

/**
 * Service for handling bid analysis supplier selection logic
 */
export class BidAnalysisService {
  
  /**
   * Determines if split award should be enabled based on supplier selection
   * Split award is true when items are awarded to different suppliers
   */
  static shouldUseSplitAward(bidLines: BidAnalysisLine[]): boolean {
    if (!bidLines || bidLines.length === 0) {
      return false;
    }

    // Get unique suppliers from bid lines
    const uniqueSuppliers = new Set();
    
    bidLines.forEach(line => {
      if (line.supplier) {
        uniqueSuppliers.add(line.supplier);
      }
    });
    
    // Split award is true if there are multiple suppliers
    return uniqueSuppliers.size > 1;
  }

  /**
   * Gets the selected response codes/IDs that should be stored in selected_responses
   * Based on your API response, this should be the response codes like "RESP-00001"
   */
  static getSelectedResponses(bidLines: BidAnalysisLine[]): string[] {
    if (!bidLines || bidLines.length === 0) {
      return [];
    }

    // Get unique response codes from bid lines
    // In your case, this would be the response codes like "RESP-00001", "RESP-00004", etc.
    const responseSet = new Set<string>();
    
    bidLines.forEach(line => {
      // You'll need to map the supplier selection back to the response code
      // For now, we'll create a placeholder logic
      if (line.supplier) {
        // This is a simplified mapping - in reality, you'd need to look up
        // which response this supplier/item combination came from
        const responseCode = `RESP-${String(line.response_item || '00000').padStart(5, '0')}`;
        responseSet.add(responseCode);
      }
    });

    return Array.from(responseSet);
  }

  /**
   * Creates the payload for updating bid analysis after supplier selection
   */
  static createUpdatePayload(
    bidAnalysis: BidAnalysis,
    bidLines: BidAnalysisLine[]
  ): Partial<BidAnalysis> {
    const split_award = this.shouldUseSplitAward(bidLines);
    const selected_responses_array = this.getSelectedResponses(bidLines);
    
    // Based on your API, selected_responses can be a single string or array
    // From your example, it looks like it's a single string when not split
    let selected_responses: string | string[];
    
    if (split_award) {
      selected_responses = selected_responses_array;
    } else {
      // Single award - use the first (and should be only) response
      selected_responses = selected_responses_array[0] || '';
    }

    return {
      split_award,
      selected_responses: selected_responses as any, // Type assertion for flexibility
    };
  }

  /**
   * Validates supplier selection before finalizing
   */
  static validateSupplierSelection(bidLines: BidAnalysisLine[]): {
    isValid: boolean;
    errors: string[];
    warnings: string[];
  } {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (!bidLines || bidLines.length === 0) {
      errors.push("No bid lines found");
      return { isValid: false, errors, warnings };
    }

    // Check if all lines have suppliers assigned
    const linesWithoutSuppliers = bidLines.filter(line => !line.supplier);
    if (linesWithoutSuppliers.length > 0) {
      errors.push(`${linesWithoutSuppliers.length} items don't have suppliers assigned`);
    }

    // Check if all lines have valid prices
    const linesWithoutPrices = bidLines.filter(line => 
      !line.unit_price || parseFloat(line.unit_price) <= 0
    );
    if (linesWithoutPrices.length > 0) {
      errors.push(`${linesWithoutPrices.length} items don't have valid unit prices`);
    }

    // Check if all lines have valid quantities
    const linesWithoutQuantities = bidLines.filter(line => 
      !line.quantity_awarded || parseFloat(line.quantity_awarded) <= 0
    );
    if (linesWithoutQuantities.length > 0) {
      errors.push(`${linesWithoutQuantities.length} items don't have valid quantities awarded`);
    }

    // Check for potential split award scenarios
    const uniqueSuppliers = new Set(bidLines.map(line => line.supplier).filter(Boolean));
    if (uniqueSuppliers.size > 1) {
      warnings.push(`Items will be split between ${uniqueSuppliers.size} suppliers`);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Groups bid lines by supplier for display and analysis
   */
  static groupBySupplier(bidLines: BidAnalysisLine[]): Record<string, {
    supplier: string | number;
    supplierName?: string;
    lines: BidAnalysisLine[];
    totalValue: number;
    itemCount: number;
  }> {
    if (!bidLines || bidLines.length === 0) {
      return {};
    }

    const groups: Record<string, any> = {};

    bidLines.forEach(line => {
      const supplierId = String(line.supplier);
      
      if (!groups[supplierId]) {
        groups[supplierId] = {
          supplier: line.supplier,
          supplierName: line.supplier_name,
          lines: [],
          totalValue: 0,
          itemCount: 0
        };
      }

      groups[supplierId].lines.push(line);
      groups[supplierId].itemCount++;
      
      // Calculate total value
      const unitPrice = parseFloat(line.unit_price || '0');
      const quantity = parseFloat(line.quantity_awarded || '0');
      const taxAmount = parseFloat(line.tax_amount || '0');
      groups[supplierId].totalValue += (unitPrice * quantity) + taxAmount;
    });

    return groups;
  }

  /**
   * Calculates summary statistics for the bid analysis
   */
  static calculateSummary(bidLines: BidAnalysisLine[]): {
    totalItems: number;
    totalSuppliers: number;
    totalValue: number;
    averageValuePerItem: number;
    isSplitAward: boolean;
  } {
    if (!bidLines || bidLines.length === 0) {
      return {
        totalItems: 0,
        totalSuppliers: 0,
        totalValue: 0,
        averageValuePerItem: 0,
        isSplitAward: false
      };
    }

    const totalItems = bidLines.length;
    const uniqueSuppliers = new Set(bidLines.map(line => line.supplier).filter(Boolean));
    const totalSuppliers = uniqueSuppliers.size;
    
    const totalValue = bidLines.reduce((sum, line) => {
      const unitPrice = parseFloat(line.unit_price || '0');
      const quantity = parseFloat(line.quantity_awarded || '0');
      const taxAmount = parseFloat(line.tax_amount || '0');
      return sum + (unitPrice * quantity) + taxAmount;
    }, 0);

    return {
      totalItems,
      totalSuppliers,
      totalValue,
      averageValuePerItem: totalItems > 0 ? totalValue / totalItems : 0,
      isSplitAward: this.shouldUseSplitAward(bidLines)
    };
  }
}
