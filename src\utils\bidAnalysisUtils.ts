import { BidAnalysis, BidAnalysisLine } from "@/types/procurement";

/**
 * Utility functions for bid analysis operations
 */

/**
 * Determines if a bid analysis should have split_award set to true
 * Split award is true when items are awarded to different suppliers
 */
export const shouldUseSplitAward = (bidLines: BidAnalysisLine[]): boolean => {
  if (!bidLines || bidLines.length === 0) {
    return false;
  }

  // Get unique suppliers from bid lines
  const uniqueSuppliers = new Set(bidLines.map(line => line.supplier));
  
  // Split award is true if there are multiple suppliers
  return uniqueSuppliers.size > 1;
};

/**
 * Gets the selected response IDs from bid analysis lines
 * This maps suppliers to their corresponding RFQ response IDs
 * Note: We need to get the actual RFQ Response IDs, not response_item IDs
 * The response_item is the specific item within a response, but selected_responses
 * should contain the parent RFQ Response IDs
 */
export const getSelectedResponseIds = (bidLines: BidAnalysisLine[]): number[] => {
  if (!bidLines || bidLines.length === 0) {
    return [];
  }

  // For now, we'll use response_item as a proxy
  // In a real implementation, you'd need to map response_item back to its parent response
  const responseIds = new Set<number>();

  bidLines.forEach(line => {
    if (line.response_item) {
      responseIds.add(line.response_item);
    }
  });

  return Array.from(responseIds);
};

/**
 * Updates bid analysis with correct split_award and selected_responses
 * based on the current bid lines
 */
export const updateBidAnalysisAwardStatus = (
  bidAnalysis: BidAnalysis,
  bidLines: BidAnalysisLine[]
): Partial<BidAnalysis> => {
  const split_award = shouldUseSplitAward(bidLines);
  const selected_responses = getSelectedResponseIds(bidLines);

  return {
    split_award,
    selected_responses,
  };
};

/**
 * Validates if supplier selection is consistent
 */
export const validateSupplierSelection = (bidLines: BidAnalysisLine[]): {
  isValid: boolean;
  errors: string[];
} => {
  const errors: string[] = [];

  if (!bidLines || bidLines.length === 0) {
    errors.push("No bid lines found");
    return { isValid: false, errors };
  }

  // Check if all lines have suppliers assigned
  const linesWithoutSuppliers = bidLines.filter(line => !line.supplier);
  if (linesWithoutSuppliers.length > 0) {
    errors.push(`${linesWithoutSuppliers.length} items don't have suppliers assigned`);
  }

  // Check if all lines have valid prices
  const linesWithoutPrices = bidLines.filter(line => !line.unit_price || parseFloat(line.unit_price) <= 0);
  if (linesWithoutPrices.length > 0) {
    errors.push(`${linesWithoutPrices.length} items don't have valid unit prices`);
  }

  // Check if all lines have valid quantities
  const linesWithoutQuantities = bidLines.filter(line => !line.quantity_awarded || parseFloat(line.quantity_awarded) <= 0);
  if (linesWithoutQuantities.length > 0) {
    errors.push(`${linesWithoutQuantities.length} items don't have valid quantities awarded`);
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Groups bid lines by supplier for display purposes
 */
export const groupBidLinesBySupplier = (bidLines: BidAnalysisLine[]): Record<number, BidAnalysisLine[]> => {
  if (!bidLines || bidLines.length === 0) {
    return {};
  }

  return bidLines.reduce((groups, line) => {
    const supplierId = line.supplier;
    if (!groups[supplierId]) {
      groups[supplierId] = [];
    }
    groups[supplierId].push(line);
    return groups;
  }, {} as Record<number, BidAnalysisLine[]>);
};

/**
 * Calculates total value for bid analysis
 */
export const calculateBidAnalysisTotal = (bidLines: BidAnalysisLine[]): number => {
  if (!bidLines || bidLines.length === 0) {
    return 0;
  }

  return bidLines.reduce((total, line) => {
    const unitPrice = parseFloat(line.unit_price || '0');
    const quantity = parseFloat(line.quantity_awarded || '0');
    const taxAmount = parseFloat(line.tax_amount || '0');
    
    return total + (unitPrice * quantity) + taxAmount;
  }, 0);
};

/**
 * Gets supplier summary for bid analysis
 */
export const getSupplierSummary = (bidLines: BidAnalysisLine[]): {
  totalSuppliers: number;
  supplierBreakdown: Record<number, {
    supplierId: number;
    supplierName?: string;
    itemCount: number;
    totalValue: number;
  }>;
} => {
  const grouped = groupBidLinesBySupplier(bidLines);
  const supplierBreakdown: Record<number, any> = {};

  Object.entries(grouped).forEach(([supplierId, lines]) => {
    const id = parseInt(supplierId);
    supplierBreakdown[id] = {
      supplierId: id,
      supplierName: lines[0]?.supplier_name,
      itemCount: lines.length,
      totalValue: calculateBidAnalysisTotal(lines)
    };
  });

  return {
    totalSuppliers: Object.keys(grouped).length,
    supplierBreakdown
  };
};
