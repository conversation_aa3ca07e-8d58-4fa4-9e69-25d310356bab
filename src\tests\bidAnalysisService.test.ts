import { BidAnalysisService } from '../services/bidAnalysisService';
import { BidAnalysis, BidAnalysisLine } from '../types/procurement';

// Mock data for testing
const mockBidAnalysis: BidAnalysis = {
  id: 1,
  split_award: false,
  recommendation_notes: "Test analysis",
  finalized_at: "2025-07-30T10:00:00Z",
  rfq: 1,
  created_by: 1,
  selected_responses: [],
};

const mockSingleSupplierLines: BidAnalysisLine[] = [
  {
    id: 1,
    unit_price: "100.00",
    quantity_awarded: "2.00",
    currency: "KES",
    total_price: "200.00",
    tax_amount: "0.00",
    bid_analysis: 1,
    rfq_item: 4,
    supplier: "SUPKAR001",
    response_item: 6,
    supplier_name: "Supplier KAR 001",
  },
  {
    id: 2,
    unit_price: "150.00",
    quantity_awarded: "3.00",
    currency: "KES",
    total_price: "450.00",
    tax_amount: "0.00",
    bid_analysis: 1,
    rfq_item: 5,
    supplier: "SUPKAR001", // Same supplier
    response_item: 7,
    supplier_name: "Supplier KAR 001",
  },
];

const mockSplitAwardLines: BidAnalysisLine[] = [
  {
    id: 1,
    unit_price: "100.00",
    quantity_awarded: "2.00",
    currency: "KES",
    total_price: "200.00",
    tax_amount: "0.00",
    bid_analysis: 1,
    rfq_item: 4,
    supplier: "SUPKAR001",
    response_item: 6,
    supplier_name: "Supplier KAR 001",
  },
  {
    id: 2,
    unit_price: "120.00",
    quantity_awarded: "5.00",
    currency: "KES",
    total_price: "600.00",
    tax_amount: "0.00",
    bid_analysis: 1,
    rfq_item: 5,
    supplier: "SUPKIT001", // Different supplier
    response_item: 8,
    supplier_name: "Supplier KIT 001",
  },
];

describe('BidAnalysisService', () => {
  describe('shouldUseSplitAward', () => {
    it('should return false for single supplier', () => {
      const result = BidAnalysisService.shouldUseSplitAward(mockSingleSupplierLines);
      expect(result).toBe(false);
    });

    it('should return true for multiple suppliers', () => {
      const result = BidAnalysisService.shouldUseSplitAward(mockSplitAwardLines);
      expect(result).toBe(true);
    });

    it('should return false for empty lines', () => {
      const result = BidAnalysisService.shouldUseSplitAward([]);
      expect(result).toBe(false);
    });
  });

  describe('createUpdatePayload', () => {
    it('should create correct payload for single supplier', () => {
      const result = BidAnalysisService.createUpdatePayload(mockBidAnalysis, mockSingleSupplierLines);
      
      expect(result.split_award).toBe(false);
      expect(typeof result.selected_responses).toBe('string');
    });

    it('should create correct payload for split award', () => {
      const result = BidAnalysisService.createUpdatePayload(mockBidAnalysis, mockSplitAwardLines);
      
      expect(result.split_award).toBe(true);
      expect(Array.isArray(result.selected_responses)).toBe(true);
    });
  });

  describe('validateSupplierSelection', () => {
    it('should validate correct supplier selection', () => {
      const result = BidAnalysisService.validateSupplierSelection(mockSingleSupplierLines);
      
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should detect missing suppliers', () => {
      const linesWithoutSupplier = mockSingleSupplierLines.map(line => ({
        ...line,
        supplier: undefined as any
      }));
      
      const result = BidAnalysisService.validateSupplierSelection(linesWithoutSupplier);
      
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });
  });

  describe('calculateSummary', () => {
    it('should calculate correct summary for single supplier', () => {
      const result = BidAnalysisService.calculateSummary(mockSingleSupplierLines);
      
      expect(result.totalItems).toBe(2);
      expect(result.totalSuppliers).toBe(1);
      expect(result.isSplitAward).toBe(false);
      expect(result.totalValue).toBe(650); // 200 + 450
    });

    it('should calculate correct summary for split award', () => {
      const result = BidAnalysisService.calculateSummary(mockSplitAwardLines);
      
      expect(result.totalItems).toBe(2);
      expect(result.totalSuppliers).toBe(2);
      expect(result.isSplitAward).toBe(true);
      expect(result.totalValue).toBe(800); // 200 + 600
    });
  });

  describe('groupBySupplier', () => {
    it('should group lines by supplier correctly', () => {
      const result = BidAnalysisService.groupBySupplier(mockSplitAwardLines);
      
      expect(Object.keys(result)).toHaveLength(2);
      expect(result['SUPKAR001']).toBeDefined();
      expect(result['SUPKIT001']).toBeDefined();
      expect(result['SUPKAR001'].lines).toHaveLength(1);
      expect(result['SUPKIT001'].lines).toHaveLength(1);
    });
  });
});

// Console log test results for manual verification
console.log('=== BID ANALYSIS SERVICE TESTS ===');

console.log('\n1. Single Supplier Test:');
const singleResult = BidAnalysisService.createUpdatePayload(mockBidAnalysis, mockSingleSupplierLines);
console.log('Split Award:', singleResult.split_award);
console.log('Selected Responses:', singleResult.selected_responses);

console.log('\n2. Split Award Test:');
const splitResult = BidAnalysisService.createUpdatePayload(mockBidAnalysis, mockSplitAwardLines);
console.log('Split Award:', splitResult.split_award);
console.log('Selected Responses:', splitResult.selected_responses);

console.log('\n3. Summary Test:');
const summary = BidAnalysisService.calculateSummary(mockSplitAwardLines);
console.log('Summary:', summary);

export {};
