# Split Award Implementation Guide

## Overview

This document explains how the split award functionality should work in your procurement system based on your requirements and API structure.

## Current Understanding

Based on your API response and requirements, here's how the system should work:

### 1. Single Supplier Award (split_award = false)

When all items in a bid analysis are awarded to the same supplier:

```json
{
  "split_award": false,
  "selected_responses": "RESP-00001",
  "bid_lines": [
    {
      "supplier": "SUPKAR001",
      "response_item": 6,
      // ... other fields
    }
    // All lines have the same supplier
  ]
}
```

### 2. Split Award (split_award = true)

When items are awarded to different suppliers:

```json
{
  "split_award": true,
  "selected_responses": ["RESP-00001", "RESP-00004"],
  "bid_lines": [
    {
      "supplier": "SUPKAR001",
      "response_item": 6,
      // ... other fields
    },
    {
      "supplier": "SUPKIT001", 
      "response_item": 8,
      // ... other fields
    }
  ]
}
```

## Implementation Components

### 1. Redux Mutations (Added)

```typescript
// Single supplier selection
selectSupplier: builder.mutation({
  query: ({ bid_analysis_id, item_id, supplier_data }) => ({
    url: `/procurement/bid-analyses/${bid_analysis_id}/select-supplier`,
    method: "POST",
    body: { item_id, ...supplier_data },
  }),
})

// Bulk supplier selection
bulkSelectSupplier: builder.mutation({
  query: ({ bid_analysis_id, selection_data }) => ({
    url: `/procurement/bid-analyses/${bid_analysis_id}/bulk-select-supplier`,
    method: "POST",
    body: selection_data,
  }),
})

// Split award creation
createSplitAward: builder.mutation({
  query: ({ bid_analysis_id, item_id, split_data }) => ({
    url: `/procurement/bid-analyses/${bid_analysis_id}/create-split-award`,
    method: "POST",
    body: { item_id, ...split_data },
  }),
})
```

### 2. Service Layer (BidAnalysisService)

The service handles the business logic:

- `shouldUseSplitAward()`: Determines if split_award should be true
- `getSelectedResponses()`: Gets the response codes/IDs for selected_responses
- `createUpdatePayload()`: Creates the update payload for the bid analysis
- `validateSupplierSelection()`: Validates the supplier selection

### 3. Auto-Update Hook (useBidAnalysisUpdater)

Automatically updates the bid analysis when suppliers are selected:

```typescript
useBidAnalysisUpdater(bidAnalysis, bidAnalysisLines?.data?.results);
```

## Workflow

### 1. Supplier Selection Process

1. User selects suppliers for items in the bid analysis
2. This creates/updates bid analysis lines with supplier information
3. The auto-updater hook detects changes in bid lines
4. It calculates the correct `split_award` and `selected_responses` values
5. It updates the bid analysis automatically

### 2. Logic for split_award

```typescript
// If all items have the same supplier -> split_award = false
// If items have different suppliers -> split_award = true
const uniqueSuppliers = new Set(bidLines.map(line => line.supplier));
const split_award = uniqueSuppliers.size > 1;
```

### 3. Logic for selected_responses

```typescript
// Single award: selected_responses = "RESP-00001" (string)
// Split award: selected_responses = ["RESP-00001", "RESP-00004"] (array)
```

## API Endpoints Needed

You'll need to implement these backend endpoints:

### 1. Select Supplier
```
POST /procurement/bid-analyses/{id}/select-supplier
Body: {
  item_id: number,
  supplier_id: string,
  unit_price: string,
  reason: string
}
```

### 2. Bulk Select Supplier
```
POST /procurement/bid-analyses/{id}/bulk-select-supplier
Body: {
  supplier_id: string,
  item_ids: number[],
  reason: string,
  apply_to_all: boolean
}
```

### 3. Create Split Award
```
POST /procurement/bid-analyses/{id}/create-split-award
Body: {
  item_id: number,
  splits: [{
    supplier: string,
    quantity: number,
    unit_price: number,
    reason: string
  }]
}
```

## Backend Logic Required

The backend should:

1. **When a supplier is selected for an item:**
   - Create/update the bid analysis line
   - Check all lines for the bid analysis
   - Determine if split_award should be true/false
   - Update selected_responses with the appropriate response codes
   - Save the bid analysis

2. **Response mapping:**
   - Map supplier selections back to their original RFQ response codes
   - Handle the relationship between response_item and the parent response

## Testing Scenarios

### Scenario 1: Single Supplier
- Select same supplier for all items
- Verify: `split_award = false`, `selected_responses = "RESP-00001"`

### Scenario 2: Split Award
- Select different suppliers for different items
- Verify: `split_award = true`, `selected_responses = ["RESP-00001", "RESP-00004"]`

### Scenario 3: Change from Split to Single
- Start with split award
- Change all items to same supplier
- Verify: `split_award = false`, `selected_responses = "RESP-00001"`

## Next Steps

1. **Implement the backend endpoints** listed above
2. **Test the frontend mutations** with the new endpoints
3. **Verify the auto-update logic** works correctly
4. **Add validation** to ensure data consistency
5. **Update the UI** to show split award status clearly

## Files Modified/Created

- `src/redux/slices/procurement.ts` - Added supplier selection mutations
- `src/services/bidAnalysisService.ts` - Business logic service
- `src/hooks/useBidAnalysisUpdater.ts` - Auto-update hook
- `src/types/procurement.ts` - Updated BidAnalysisLine interface
- `src/components/BidAnalysis/SupplierSelectionDemo.tsx` - Demo component

The system is now ready for backend implementation and testing!
