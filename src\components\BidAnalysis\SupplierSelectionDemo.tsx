import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { BidAnalysisService } from '@/services/bidAnalysisService';
import { BidAnalysis, BidAnalysisLine } from '@/types/procurement';

interface SupplierSelectionDemoProps {
  bidAnalysis: BidAnalysis;
  bidLines: BidAnalysisLine[];
  onUpdate: (updates: Partial<BidAnalysis>) => void;
}

/**
 * Demo component showing how supplier selection should work
 * This demonstrates the logic for updating split_award and selected_responses
 */
export const SupplierSelectionDemo: React.FC<SupplierSelectionDemoProps> = ({
  bidAnalysis,
  bidLines,
  onUpdate
}) => {
  const [currentLines, setCurrentLines] = useState<BidAnalysisLine[]>(bidLines);
  
  // Calculate current state
  const summary = BidAnalysisService.calculateSummary(currentLines);
  const validation = BidAnalysisService.validateSupplierSelection(currentLines);
  const supplierGroups = BidAnalysisService.groupBySupplier(currentLines);
  const updates = BidAnalysisService.createUpdatePayload(bidAnalysis, currentLines);

  // Simulate supplier selection for demo
  const simulateSupplierSelection = (scenario: 'single' | 'split') => {
    let newLines: BidAnalysisLine[] = [];

    if (scenario === 'single') {
      // All items awarded to same supplier (SUPKAR001)
      newLines = currentLines.map(line => ({
        ...line,
        supplier: 'SUPKAR001',
        supplier_name: 'Supplier KAR 001',
        response_item: 6, // All from same response
      }));
    } else {
      // Split award - different suppliers for different items
      newLines = currentLines.map((line, index) => ({
        ...line,
        supplier: index % 2 === 0 ? 'SUPKAR001' : 'SUPKIT001',
        supplier_name: index % 2 === 0 ? 'Supplier KAR 001' : 'Supplier KIT 001',
        response_item: index % 2 === 0 ? 6 : 8, // Different responses
      }));
    }

    setCurrentLines(newLines);
  };

  // Auto-update when lines change
  useEffect(() => {
    const newUpdates = BidAnalysisService.createUpdatePayload(bidAnalysis, currentLines);
    onUpdate(newUpdates);
  }, [currentLines, bidAnalysis, onUpdate]);

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Supplier Selection Demo</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Button 
              onClick={() => simulateSupplierSelection('single')}
              variant="outline"
            >
              Single Supplier Award
            </Button>
            <Button 
              onClick={() => simulateSupplierSelection('split')}
              variant="outline"
            >
              Split Award (Multiple Suppliers)
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-semibold mb-2">Current Status</h4>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>Split Award:</span>
                  <Badge variant={summary.isSplitAward ? "default" : "secondary"}>
                    {summary.isSplitAward ? "Yes" : "No"}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span>Total Suppliers:</span>
                  <span>{summary.totalSuppliers}</span>
                </div>
                <div className="flex justify-between">
                  <span>Total Items:</span>
                  <span>{summary.totalItems}</span>
                </div>
                <div className="flex justify-between">
                  <span>Total Value:</span>
                  <span>${summary.totalValue.toFixed(2)}</span>
                </div>
              </div>
            </div>

            <div>
              <h4 className="font-semibold mb-2">Updates to Apply</h4>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>split_award:</span>
                  <code className="text-sm bg-gray-100 px-2 py-1 rounded">
                    {String(updates.split_award)}
                  </code>
                </div>
                <div>
                  <span>selected_responses:</span>
                  <code className="text-sm bg-gray-100 px-2 py-1 rounded block mt-1">
                    {JSON.stringify(updates.selected_responses)}
                  </code>
                </div>
              </div>
            </div>
          </div>

          {validation.errors.length > 0 && (
            <div className="bg-red-50 border border-red-200 rounded p-3">
              <h5 className="font-semibold text-red-800 mb-1">Validation Errors:</h5>
              <ul className="text-red-700 text-sm space-y-1">
                {validation.errors.map((error, index) => (
                  <li key={index}>• {error}</li>
                ))}
              </ul>
            </div>
          )}

          {validation.warnings.length > 0 && (
            <div className="bg-yellow-50 border border-yellow-200 rounded p-3">
              <h5 className="font-semibold text-yellow-800 mb-1">Warnings:</h5>
              <ul className="text-yellow-700 text-sm space-y-1">
                {validation.warnings.map((warning, index) => (
                  <li key={index}>• {warning}</li>
                ))}
              </ul>
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Supplier Breakdown</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Object.entries(supplierGroups).map(([supplierId, group]) => (
              <div key={supplierId} className="border rounded p-3">
                <div className="flex justify-between items-center mb-2">
                  <h5 className="font-semibold">
                    {group.supplierName || `Supplier ${supplierId}`}
                  </h5>
                  <Badge variant="outline">
                    {group.itemCount} items - ${group.totalValue.toFixed(2)}
                  </Badge>
                </div>
                <div className="text-sm text-gray-600">
                  Items: {group.lines.map(line => `Item ${line.rfq_item}`).join(', ')}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Bid Lines Details</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {currentLines.map((line, index) => (
              <div key={index} className="flex justify-between items-center p-2 border rounded">
                <span>Item {line.rfq_item}</span>
                <span>Supplier: {line.supplier_name || line.supplier || 'Not assigned'}</span>
                <span>Price: ${line.unit_price}</span>
                <span>Qty: {line.quantity_awarded}</span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
