import { useEffect } from 'react';
import { useUpdateBidAnalysisMutation } from '@/redux/slices/procurement';
import { BidAnalysisService } from '@/services/bidAnalysisService';
import { BidAnalysis, BidAnalysisLine } from '@/types/procurement';

/**
 * Custom hook that automatically updates bid analysis split_award and selected_responses
 * when bid lines change (i.e., when suppliers are selected)
 */
export const useBidAnalysisUpdater = (
  bidAnalysis: BidAnalysis | undefined,
  bidLines: BidAnalysisLine[] | undefined,
  enabled: boolean = true
) => {
  const [updateBidAnalysis] = useUpdateBidAnalysisMutation();

  useEffect(() => {
    if (!enabled || !bidAnalysis || !bidLines || bidLines.length === 0) {
      return;
    }

    // Calculate what the split_award and selected_responses should be
    const updates = BidAnalysisService.createUpdatePayload(bidAnalysis, bidLines);

    // Check if updates are needed
    const needsUpdate =
      bidAnalysis.split_award !== updates.split_award ||
      JSON.stringify(bidAnalysis.selected_responses) !== JSON.stringify(updates.selected_responses);

    if (needsUpdate) {
      // Update the bid analysis
      updateBidAnalysis({
        id: bidAnalysis.id!,
        split_award: updates.split_award,
        selected_responses: updates.selected_responses,
      }).catch((error) => {
        console.error('Failed to update bid analysis award status:', error);
      });
    }
  }, [bidAnalysis, bidLines, enabled, updateBidAnalysis]);

  return {
    updateBidAnalysis
  };
};

/**
 * Hook for manually triggering bid analysis updates
 */
export const useBidAnalysisManualUpdater = () => {
  const [updateBidAnalysis] = useUpdateBidAnalysisMutation();

  const updateAwardStatus = async (
    bidAnalysis: BidAnalysis,
    bidLines: BidAnalysisLine[]
  ) => {
    const updates = BidAnalysisService.createUpdatePayload(bidAnalysis, bidLines);
    
    try {
      await updateBidAnalysis({
        id: bidAnalysis.id!,
        split_award: updates.split_award,
        selected_responses: updates.selected_responses,
      }).unwrap();
      
      return { success: true, updates };
    } catch (error) {
      console.error('Failed to update bid analysis award status:', error);
      return { success: false, error };
    }
  };

  return {
    updateAwardStatus
  };
};
