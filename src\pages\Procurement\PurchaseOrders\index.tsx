import { Screen } from "@/app-components/layout/screen";
import AddPurchaseOrder from "./modals/AddPurchaseOrder";
import CancelPurchaseOrder from "./modals/CancelPurchaseOrder";
import EditPurchaseOrder from "./modals/EditPurchaseOrder";
import { useState } from "react";
import {
  useGetPurchaseOrdersQuery,
  useGetSuppliersQuery,
  useGetProcurementStoresQuery,
  useSubmitPurchaseOrderMutation,
  useApprovePurchaseOrderMutation,
  useCancelPurchaseOrderMutation,
  useDeletePurchaseOrderMutation,
  useGenerateGRNFromPurchaseOrderMutation,
} from "@/redux/slices/procurement";
import { ColumnDef } from "@tanstack/react-table";
import { PurchaseOrder } from "@/types/procurement";
import { Link } from "react-router-dom";
import { DataTable } from "@/components/custom/tables/Table1";
import { searchDebouncer } from "@/utils/debouncers";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import {
  Plus,
  MoreHorizontal,
  Eye,
  Edit,
  Send,
  CheckCircle,
  XCircle,
  Trash2,
  Mail,
  Download,
  Search,
  Truck
} from "lucide-react";
import { toast } from "@/components/custom/Toast/MyToast";

const PurchaseOrdersIndex = () => {
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [isCancelModalOpen, setIsCancelModalOpen] = useState(false);
  const [selectedPurchaseOrder, setSelectedPurchaseOrder] = useState<any>(null);
  const [filters, setFilters] = useState({
    status: "all",
    supplier: "all",
    delivery_location: "all",
    search: "",
  });

  // Transform filters for API (remove "all" values)
  const apiFilters = Object.fromEntries(
    Object.entries(filters).filter(([key, value]) => value !== "all" && value !== "")
  );

  // API queries
  const {
    data: purchaseOrdersData,
    isLoading,
    error,
    isFetching
  } = useGetPurchaseOrdersQuery(apiFilters);
  const { data: suppliers } = useGetSuppliersQuery({});
  const { data: stores } = useGetProcurementStoresQuery({});

  // Mutation hooks
  const [submitPurchaseOrder, { isLoading: isSubmitting }] = useSubmitPurchaseOrderMutation();
  const [approvePurchaseOrder, { isLoading: isApproving }] = useApprovePurchaseOrderMutation();
  const [cancelPurchaseOrder, { isLoading: isCancelling }] = useCancelPurchaseOrderMutation();
  const [deletePurchaseOrder, { isLoading: isDeleting }] = useDeletePurchaseOrderMutation();
  const [generateGRN, { isLoading: isGeneratingGRN }] = useGenerateGRNFromPurchaseOrderMutation();

  // Handler functions
  const handleSubmit = async (id: number) => {
    try {
      await submitPurchaseOrder(id).unwrap();
      toast.success("Purchase order submitted successfully");
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to submit purchase order");
    }
  };

  const handleApprove = async (id: number) => {
    try {
      await approvePurchaseOrder(id).unwrap();
      toast.success("Purchase order approved successfully");
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to approve purchase order");
    }
  };

  const handleEdit = (purchaseOrder: any) => {
    setSelectedPurchaseOrder(purchaseOrder);
    setShowEditModal(true);
  };

  const handleCancel = (purchaseOrder: any) => {
    setSelectedPurchaseOrder(purchaseOrder);
    setIsCancelModalOpen(true);
  };

  const handleDelete = async (id: number) => {
    if (window.confirm("Are you sure you want to delete this purchase order?")) {
      try {
        await deletePurchaseOrder(id).unwrap();
        toast.success("Purchase order deleted successfully");
      } catch (error: any) {
        toast.error(error?.data?.message || "Failed to delete purchase order");
      }
    }
  };

  const handleGenerateGRN = async (purchaseOrder: any) => {
    try {
      const payload = {
        id: purchaseOrder.id,
        po_number: purchaseOrder.po_number,
        supplier: purchaseOrder.supplier,
        status: "Draft",
        delivery_location: purchaseOrder.delivery_location || "",
        payment_terms: purchaseOrder.payment_terms || "",
        delivery_date: purchaseOrder.delivery_date || new Date().toISOString().split('T')[0],
        created_by: purchaseOrder.created_by
      };

      const result = await generateGRN(payload).unwrap();
      toast.success(`GRN ${result.grn?.grn_number} created successfully`);
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to generate GRN");
    }
  };

  // Status badge helper
  const getStatusBadge = (status: string) => {
    const statusConfig = {
      "Draft": { variant: "secondary" as const, className: "bg-gray-100 text-gray-800" },
      "Submitted": { variant: "secondary" as const, className: "bg-blue-100 text-blue-800" },
      "Approved": { variant: "secondary" as const, className: "bg-green-100 text-green-800" },
      "Cancelled": { variant: "secondary" as const, className: "bg-red-100 text-red-800" },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig["Draft"];
    return <Badge variant={config.variant} className={config.className}>{status}</Badge>;
  };

  // Search handler
  const handleSearch = (value: string) => {
    setFilters(prev => ({ ...prev, search: value }));
  };

  // Table columns
  const columns: ColumnDef<PurchaseOrder>[] = [
    {
      accessorKey: "po_number",
      header: "PO Number",
      cell: ({ row }) => (
        <div className="flex flex-col">
          <Link
            to={`/procurement/purchase-orders/${row.original.id}`}
            className="font-semibold text-blue-600 hover:text-blue-800 hover:underline"
            title="Click to view details"
          >
            {row.original.po_number}
          </Link>
          <span className="text-xs text-gray-500">
            ID: {row.original.id}
          </span>
        </div>
      ),
    },
    {
      accessorKey: "supplier",
      header: "Supplier",
      cell: ({ row }) => (
        <div>
          <div className="font-medium">{row.original.supplier}</div>
          <div className="text-xs text-gray-500">Supplier Code</div>
        </div>
      ),
    },
    {
      accessorKey: "purchase_order_items",
      header: "Total Value",
      cell: ({ row }) => {
        const items = row.original.purchase_order_items || [];
        const totalValue = items.reduce((sum, item) => sum + parseFloat(item.total_price || "0"), 0);
        return (
          <div className="font-medium">
            KES {totalValue.toLocaleString()}
            <div className="text-xs text-gray-500">{items.length} item(s)</div>
          </div>
        );
      },
    },
    {
      accessorKey: "delivery_date",
      header: "Delivery Info",
      cell: ({ row }) => (
        <div>
          <div className="font-medium">
            {row.original.delivery_date ? new Date(row.original.delivery_date).toLocaleDateString() : "Not set"}
          </div>
          <div className="text-xs text-gray-500">
            {row.original.delivery_location || "Location not set"}
          </div>
        </div>
      ),
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => getStatusBadge(row.original.status || "Draft"),
    },
    {
      accessorKey: "created_at",
      header: "Created",
      cell: ({ row }) => (
        <div className="text-sm">
          <div className="font-medium">
            {new Date(row.original.created_at || "").toLocaleDateString()}
          </div>
          <div className="text-xs text-gray-500">
            By: {row.original.created_by}
          </div>
        </div>
      ),
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const purchaseOrder = row.original;
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem asChild>
                <Link to={`/procurement/purchase-orders/${purchaseOrder.id}`}>
                  <Eye className="mr-2 h-4 w-4" />
                  View Details
                </Link>
              </DropdownMenuItem>

              {purchaseOrder.status === "Draft" && (
                <>
                  <DropdownMenuItem onClick={() => handleEdit(purchaseOrder)}>
                    <Edit className="mr-2 h-4 w-4" />
                    Edit Details
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleSubmit(purchaseOrder.id!)}>
                    <Send className="mr-2 h-4 w-4" />
                    Submit for Approval
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => handleDelete(purchaseOrder.id!)}
                    className="text-red-600"
                  >
                    <Trash2 className="mr-2 h-4 w-4" />
                    Delete
                  </DropdownMenuItem>
                </>
              )}

              {purchaseOrder.status === "Submitted" && (
                <>
                  <DropdownMenuItem onClick={() => handleApprove(purchaseOrder.id!)}>
                    <CheckCircle className="mr-2 h-4 w-4" />
                    Approve
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => handleCancel(purchaseOrder)}
                    className="text-red-600"
                  >
                    <XCircle className="mr-2 h-4 w-4" />
                    Cancel
                  </DropdownMenuItem>
                </>
              )}

              {purchaseOrder.status === "Approved" && (
                <DropdownMenuItem
                  onClick={() => handleGenerateGRN(purchaseOrder)}
                  disabled={isGeneratingGRN}
                  className="text-green-600"
                >
                  <Truck className="mr-2 h-4 w-4" />
                  {isGeneratingGRN ? "Generating GRN..." : "Generate GRN"}
                </DropdownMenuItem>
              )}

              {(purchaseOrder.status === "Draft" || purchaseOrder.status === "Cancelled") && (
                <DropdownMenuItem
                  onClick={() => handleDelete(purchaseOrder.id!)}
                  className="text-red-600"
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  return (
    <Screen>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-800">Purchase Orders</h1>
            <p className="text-gray-600 mt-1">Manage purchase orders and supplier communications</p>
          </div>
          <div className="flex items-center gap-2">
            <Button onClick={() => setShowAddModal(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Create Purchase Order
            </Button>
          </div>
        </div>

        {/* Filters */}
        <div className="flex flex-wrap gap-4 p-4 bg-gray-50 rounded-lg">
          <div className="flex items-center gap-2">
            <Search className="h-4 w-4 text-gray-500" />
            <Input
              placeholder="Search purchase orders..."
              className="w-64"
              onChange={(e) => handleSearch(e.target.value)}
            />
          </div>

          <Select value={filters.status} onValueChange={(value) => setFilters(prev => ({ ...prev, status: value }))}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Statuses</SelectItem>
              <SelectItem value="Draft">Draft</SelectItem>
              <SelectItem value="Pending Approval">Pending Approval</SelectItem>
              <SelectItem value="Approved">Approved</SelectItem>
              <SelectItem value="Sent">Sent</SelectItem>
              <SelectItem value="Cancelled">Cancelled</SelectItem>
            </SelectContent>
          </Select>

          <Select value={filters.supplier} onValueChange={(value) => setFilters(prev => ({ ...prev, supplier: value }))}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Filter by supplier" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Suppliers</SelectItem>
              {suppliers?.data?.results?.map((supplier: any) => (
                <SelectItem key={supplier.id} value={supplier.id.toString()}>
                  {supplier.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

        </div>

        {/* Loading Indicator */}
        {(isFetching || isSubmitting || isApproving || isCancelling || isDeleting) && (
          <div className="flex items-center justify-center py-4 bg-blue-50 rounded-lg mb-4">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
            <span className="text-blue-800 text-sm">
              {isFetching && "Loading data..."}
              {isSubmitting && "Submitting purchase order..."}
              {isApproving && "Approving purchase order..."}
              {isCancelling && "Cancelling purchase order..."}
              {isDeleting && "Deleting purchase order..."}
            </span>
          </div>
        )}

        {/* Loading State */}
        {isLoading && (
          <div className="flex justify-center items-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <span className="ml-2">Loading purchase orders...</span>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
            <div className="flex items-center">
              <XCircle className="h-5 w-5 text-red-500 mr-2" />
              <span className="text-red-800">
                Failed to load purchase orders. Please try again.
              </span>
            </div>
          </div>
        )}

        {/* Data Table */}
        <DataTable
          columns={columns}
          data={purchaseOrdersData?.results || []}
          loading={isLoading}
        />

        {/* Add Purchase Order Modal */}
        <AddPurchaseOrder
          open={showAddModal}
          onClose={() => setShowAddModal(false)}
        />

        {/* Edit Purchase Order Modal */}
        <EditPurchaseOrder
          open={showEditModal}
          onClose={() => {
            setShowEditModal(false);
            setSelectedPurchaseOrder(null);
          }}
          purchaseOrder={selectedPurchaseOrder}
          onSuccess={() => {
            setShowEditModal(false);
            setSelectedPurchaseOrder(null);
          }}
        />

        {isCancelModalOpen && selectedPurchaseOrder && (
          <CancelPurchaseOrder
            isOpen={isCancelModalOpen}
            onClose={() => {
              setIsCancelModalOpen(false);
              setSelectedPurchaseOrder(null);
            }}
            purchaseOrderId={selectedPurchaseOrder.id}
            purchaseOrderNumber={selectedPurchaseOrder.po_number}
          />
        )}
      </div>
    </Screen>
  );
};

export default PurchaseOrdersIndex;
